"""
DAG for monitoring model performance and triggering retraining.

This DAG monitors the performance of deployed ML models, analyzes mapping
quality metrics, and triggers retraining when performance degrades below
acceptable thresholds.

Author: Airflow LLM Service Mapper Team
Version: 1.0.0
"""

from __future__ import annotations

import json
import logging
import pendulum
from typing import Dict, List, Any, Optional

from airflow.decorators import dag, task
from airflow.datasets import Dataset
from airflow.exceptions import AirflowException
from airflow.models.param import Param
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.context import Context

# Import shared modules
import sys
import os

# Add shared directory to Python path BEFORE importing shared modules
# In Docker container, shared is mounted at /opt/airflow/shared
shared_path = '/opt/airflow/shared'
if os.path.exists(shared_path) and shared_path not in sys.path:
    sys.path.insert(0, shared_path)  # Use insert(0, ...) for higher priority
else:
    # Fallback for local development (relative path)
    try:
        # Try to use __file__ if available
        shared_path = os.path.join(os.path.dirname(__file__), '..', '..', 'shared')
    except NameError:
        # If __file__ is not defined, try current working directory
        shared_path = os.path.join(os.getcwd(), 'shared')

    if os.path.exists(shared_path) and shared_path not in sys.path:
        sys.path.insert(0, shared_path)

# Now import shared modules
from shared.common import constants, validation_utils
from shared.common.model_management import get_model_performance_metrics
from shared.common.definition_store import DefinitionStore

# Configure logging
logger = logging.getLogger(__name__)

# Define datasets for lineage tracking
OPERATIONAL_MAPPING_DS = Dataset(constants.OPERATIONAL_MAPPING_DATASET_URI)
MONITORING_METRICS_DS = Dataset(constants.MONITORING_METRICS_DATASET_URI)

@dag(
    dag_id="05_monitoring_and_retraining_trigger",
    description="Monitor model performance and trigger retraining when needed",
    schedule="@hourly",  # Regular monitoring schedule
    start_date=pendulum.datetime(2024, 1, 1, tz="UTC"),
    catchup=False,
    max_active_runs=1,  # Only one monitoring run at a time
    max_active_tasks=5,
    default_args={
        "owner": "llm-mapper-team",
        "depends_on_past": False,
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": pendulum.duration(minutes=5),
    },
    tags=["llm-mapper", "monitoring", "retraining", "mlops"],
    params={
        "lookback_hours": Param(
            24,
            type="integer",
            description="Hours of historical data to analyze."
        ),
        "accuracy_threshold": Param(
            0.85,
            type="number",
            description="Minimum accuracy threshold for model performance."
        ),
        "confidence_threshold": Param(
            0.8,
            type="number",
            description="Minimum confidence threshold for predictions."
        ),
        "error_rate_threshold": Param(
            0.1,
            type="number",
            description="Maximum acceptable error rate."
        ),
        "min_samples_for_analysis": Param(
            100,
            type="integer",
            description="Minimum number of samples required for analysis."
        ),
        "enable_auto_retraining": Param(
            True,
            type="boolean",
            description="Whether to automatically trigger retraining."
        ),
        "notification_email": Param(
            "<EMAIL>",
            type="string",
            description="Email address for monitoring notifications."
        ),
    },
    doc_md="""
    ## Monitoring and Retraining Trigger DAG

    This DAG continuously monitors the performance of deployed ML models,
    analyzes mapping quality metrics, and automatically triggers retraining
    when performance degrades below acceptable thresholds.

    ### Workflow
    1. **Collect Metrics**: Gathers performance data from operational mappings
    2. **Analyze Performance**: Evaluates model accuracy, confidence, and error rates
    3. **Detect Drift**: Identifies data drift and concept drift
    4. **Generate Reports**: Creates performance reports and visualizations
    5. **Trigger Retraining**: Automatically starts retraining if needed
    6. **Send Notifications**: Alerts administrators of performance issues

    ### Monitoring Metrics
    - **Accuracy**: Percentage of correct mappings
    - **Confidence**: Average confidence scores
    - **Error Rate**: Percentage of failed mappings
    - **Latency**: Average response time
    - **Throughput**: Requests per hour
    - **Data Drift**: Changes in input data distribution

    ### Inputs
    - **lookback_hours**: Time window for analysis
    - **accuracy_threshold**: Minimum acceptable accuracy
    - **confidence_threshold**: Minimum acceptable confidence
    - **error_rate_threshold**: Maximum acceptable error rate
    - **min_samples_for_analysis**: Minimum data points needed
    - **enable_auto_retraining**: Auto-trigger retraining flag
    - **notification_email**: Alert recipient email

    ### Outputs
    - Performance metrics and reports
    - Drift detection results
    - Retraining recommendations
    - Automated retraining triggers
    - Monitoring dashboards

    ### Dependencies
    - Requires: 04_operational_mapping_pipeline_dag (for operational data)
    - Triggers: 02_generate_training_data_dag, 03_train_ml_model_dag (for retraining)

    ### Alerting
    - Email notifications for performance degradation
    - Dashboard alerts for real-time monitoring
    - Automated escalation for critical issues
    """,
)
def monitoring_and_retraining_trigger_dag():
    """
    Main DAG function for monitoring and retraining triggers.

    Returns:
        DAG: Configured Airflow DAG instance
    """

    @task
    def collect_performance_metrics(
        lookback_hours: int,
        min_samples_for_analysis: int,
        **context: Context
    ) -> Dict[str, Any]:
        """
        Collect performance metrics from operational mappings.

        Args:
            lookback_hours: Hours of historical data to collect
            min_samples_for_analysis: Minimum samples required
            **context: Airflow context

        Returns:
            Dict containing collected metrics and metadata

        Raises:
            AirflowException: If insufficient data is available
        """
        logger.info(f"Collecting performance metrics for last {lookback_hours} hours")

        # Calculate time window
        end_time = pendulum.now()
        start_time = end_time.subtract(hours=lookback_hours)

        # Collect metrics from operational mappings
        try:
            metrics = get_model_performance_metrics(
                start_time=start_time,
                end_time=end_time
            )

            # Validate sufficient data
            total_samples = metrics.get("total_mappings", 0)
            if total_samples < min_samples_for_analysis:
                logger.warning(
                    f"Insufficient data for analysis: {total_samples} samples "
                    f"(minimum required: {min_samples_for_analysis})"
                )
                return {
                    "status": "insufficient_data",
                    "total_samples": total_samples,
                    "min_required": min_samples_for_analysis,
                    "collection_timestamp": pendulum.now().isoformat()
                }

            logger.info(f"Successfully collected metrics for {total_samples} samples")
            return {
                "status": "success",
                "metrics": metrics,
                "total_samples": total_samples,
                "time_window": {
                    "start": start_time.isoformat(),
                    "end": end_time.isoformat()
                },
                "collection_timestamp": pendulum.now().isoformat()
            }

        except Exception as e:
            error_msg = f"Failed to collect performance metrics: {str(e)}"
            logger.error(error_msg)
            raise AirflowException(error_msg)

# Instantiate the DAG
monitoring_and_retraining_trigger_dag_instance = monitoring_and_retraining_trigger_dag()