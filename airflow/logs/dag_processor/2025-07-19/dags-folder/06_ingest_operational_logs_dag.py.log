{"timestamp":"2025-07-19T18:04:57.505138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:04:57.851290","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":32,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:08:01.052165","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:08:01.310573","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":32,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:15:42.844533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:43.039872","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":32,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:16:19.672412","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:19.733710","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":32,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:16:52.342870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:52.696857","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":32,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:17:41.681767","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:17:56.087475","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":32,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:32:08.512637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:09.006563","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:33:12.636704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:12.723448","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:33:44.172526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:44.232195","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:34:14.944689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:14.967943","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:34:46.754369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:46.836573","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:35:19.086042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:19.297887","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:35:49.513997","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:49.538960","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:36:19.771870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:19.788262","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:36:55.034501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:55.117979","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:37:25.730367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:25.784342","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:37:56.574384","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:56.607357","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:38:27.036747","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:27.078230","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:39:07.793674","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:39:08.128929","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:39:40.182514","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:39:40.285106","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:41:37.887009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:41:39.341510","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:42:27.783168","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:42:27.942848","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:06:09.547604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:06:09.663551","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:06:45.331157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:06:45.396565","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:07:16.542278","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:07:16.589502","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:07:47.424003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:07:47.447101","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:08:24.293244","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:08:24.345314","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:08:55.408301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:08:55.444417","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:09:26.424883","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:09:26.457886","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:10:25.007144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:10:25.237371","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:10:59.050348","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:10:59.115209","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:11:29.446803","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:11:29.472834","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:12:00.187841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:12:00.298963","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:12:31.467885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:12:31.487607","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:12:58.884522","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:12:58.963676","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:13:29.767462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:13:29.830146","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:14:00.561728","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:14:00.587166","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:14:31.181343","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:14:31.242246","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:15:02.201700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:15:02.234812","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:15:33.000978","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:15:33.018160","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:15:55.917733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:15:55.945265","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:16:29.147175","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:16:29.212000","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:16:59.704427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:16:59.749447","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:17:31.570763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:17:32.203745","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:18:03.104189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:18:03.131516","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:18:34.106436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:18:34.124901","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:25:59.947595","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:26:00.013471","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:26:34.003313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:26:34.087563","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:27:04.952994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:27:04.994495","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:27:35.926234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:27:35.944503","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:28:06.667844","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:28:06.689386","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:28:37.447515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:28:37.471201","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:29:08.244784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:29:08.259165","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:29:38.975244","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:29:38.991792","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:30:09.719627","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:30:09.736841","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:30:40.361132","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:30:40.376860","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:31:11.217270","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:31:11.231058","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:31:41.924703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:31:41.939861","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:32:12.696191","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:32:12.718652","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:32:43.351985","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:32:43.370809","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:33:14.181226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:33:14.202628","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:33:44.948982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:33:44.968077","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:34:15.777602","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:34:15.794189","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:34:46.587856","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:34:46.607669","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:35:17.402687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:35:17.420703","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:35:48.248407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:35:48.267989","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:36:18.923011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:36:18.942056","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:36:49.688471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:36:49.705585","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:37:20.554622","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:37:20.576654","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:37:51.342410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:37:51.357663","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:38:22.069682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:38:22.086709","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:38:52.849102","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:38:52.866894","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:39:23.664434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:39:23.685128","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:39:54.477048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:39:54.491139","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:40:25.204158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:40:25.221442","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:40:56.068463","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:40:56.087276","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:41:26.813412","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:41:26.827007","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:41:57.678029","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:41:57.717871","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:42:28.590065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:42:28.634955","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:42:59.341178","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:42:59.360936","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:43:30.055297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:43:30.075825","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:44:00.758311","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:44:00.778918","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:44:32.039879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:44:32.103981","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:45:02.601052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:45:02.643204","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:45:33.011924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:45:33.044205","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:46:03.348434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:46:03.379039","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:46:33.681818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:46:33.711157","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:47:04.671505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:47:04.692328","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:47:35.596812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:47:35.614352","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:48:06.454216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:48:06.474053","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:48:37.452947","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:48:37.476913","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:49:08.372224","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:49:08.389439","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:49:39.228860","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:49:39.248793","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:50:10.059155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:50:10.080635","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:50:40.847311","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:50:40.862722","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:51:11.714301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:51:11.737169","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:51:41.975123","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:51:41.998615","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:52:13.144083","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:52:13.162340","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:52:43.992263","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:52:44.006595","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:53:14.869677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:53:14.894830","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:53:45.640232","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:53:45.655004","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:54:16.388030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:54:16.408185","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:54:47.180001","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:54:47.193008","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:55:18.219670","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:55:18.241284","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:55:49.150040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:55:49.169628","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:56:20.301379","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:56:20.322156","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:56:51.651949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:56:51.681868","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:57:22.387598","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:57:22.409423","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:57:53.142490","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:57:53.169963","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:58:23.919469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:58:23.932836","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:58:54.625526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:58:54.638986","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:59:25.773186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:59:25.812161","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:59:56.146533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:59:56.176918","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:00:27.023410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:00:27.038970","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:00:58.020007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:00:58.040626","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:01:28.987503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:01:29.004398","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:02:00.061687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:02:00.087965","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:02:31.205995","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:02:31.323603","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:08:06.172237","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:08:06.533775","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:08:43.154998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:08:43.315505","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:09:13.501861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:09:13.528078","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:09:44.506111","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:09:44.526988","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:10:15.611580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:10:15.638185","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:10:45.903361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:10:45.939086","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:11:16.732183","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:11:16.756910","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:11:47.699213","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:11:47.738349","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:12:19.006956","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:12:19.038342","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:12:49.978053","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:12:50.013774","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:13:20.529574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:13:20.615040","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:13:50.960959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:13:50.990546","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:14:22.094108","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:14:22.124266","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:14:52.555498","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:14:52.588251","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:15:22.870618","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:15:22.895936","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:15:33.937701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:15:33.989592","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"SyntaxError","exc_value":"invalid syntax (06_ingest_operational_logs_dag.py, line 31)","exc_notes":[],"syntax_error":{"offset":68,"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","line":"        shared_path = os.path.join(os.path.dirname(__file__), '..'.*$\n","lineno":31,"msg":"invalid syntax"},"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":995,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap_external>","lineno":1133,"name":"get_code"},{"filename":"<frozen importlib._bootstrap_external>","lineno":1063,"name":"source_to_code"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:16:04.315218","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:16:04.339053","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"SyntaxError","exc_value":"invalid syntax (06_ingest_operational_logs_dag.py, line 31)","exc_notes":[],"syntax_error":{"offset":68,"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","line":"        shared_path = os.path.join(os.path.dirname(__file__), '..'.*$\n","lineno":31,"msg":"invalid syntax"},"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":995,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap_external>","lineno":1133,"name":"get_code"},{"filename":"<frozen importlib._bootstrap_external>","lineno":1063,"name":"source_to_code"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:16:15.881590","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:16:16.580411","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:16:47.980688","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:16:48.016908","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:17:18.684351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:17:18.710409","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:17:23.117772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:17:23.341244","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:17:53.800912","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:17:53.823363","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:18:24.883192","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:18:24.905584","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:18:55.795352","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:18:55.822398","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:19:26.899145","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:19:26.914635","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:19:57.917468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:19:57.976420","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:20:29.172914","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:20:29.240540","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:21:00.219770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:21:00.290775","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:24:11.897203","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:24:12.125934","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:24:50.808511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:24:50.871510","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:25:22.745056","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:25:23.183795","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:25:54.185362","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:25:54.201600","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:26:25.047502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:26:25.078512","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:26:55.837369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:26:55.873577","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:27:26.831668","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:27:26.851284","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:27:57.855360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:27:58.222567","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:28:29.354391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:28:29.379883","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:29:00.191381","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:29:00.227278","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:29:31.306949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:29:31.455634","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:29:35.645560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:29:35.824266","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:29:50.433390","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:29:50.753799","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:30:00.477545","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:30:00.699016","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:30:23.782797","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:30:23.883491","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:30:33.697171","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:30:33.752013","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:30:44.526304","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:30:44.643644","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:31:00.247880","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:31:00.387688","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:31:16.037466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:31:16.209110","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:31:24.639027","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:31:24.853750","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:31:55.115987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:31:55.170478","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:32:26.105385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:32:26.132295","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:32:56.570769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:32:56.594771","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:34:44.368229","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:34:44.750395","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:35:21.463126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:35:21.575766","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:35:51.825607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:35:51.861279","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:36:23.011995","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:36:23.041089","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:36:54.020800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:36:54.056765","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:37:24.941776","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:37:24.966905","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:37:55.851038","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:37:55.868016","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:38:26.701199","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:38:26.725998","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:38:58.057028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:38:58.210791","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:39:29.474187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:39:29.563616","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:40:00.078887","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:40:00.154810","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:40:31.966534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:40:32.541165","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:41:04.742519","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:41:05.104629","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:41:10.631075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:41:10.842129","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:41:42.001502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:41:42.032695","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:42:13.022787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:42:13.053830","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:42:44.318011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:42:44.355324","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:43:15.622923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:43:15.850843","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:43:46.207126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:43:46.247045","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:46:06.793321","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:46:06.986883","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:47:06.112157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:47:06.491825","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:47:58.501767","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:48:07.038081","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:48:48.549687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:48:48.943711","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:49:21.025470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:49:21.258219","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:49:52.516625","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:49:52.647305","level":"error","event":"Failed to import: /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/06_ingest_operational_logs_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:50:19.930204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:50:20.135289Z","level":"info","event":"Warning: Could not import shared modules: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:51:15.763226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:51:15.812384Z","level":"info","event":"Warning: Could not import shared modules: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:51:50.036524","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/06_ingest_operational_logs_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:51:50.068604Z","level":"info","event":"Warning: Could not import shared modules: No module named 'shared'","chan":"stdout","logger":"processor"}
